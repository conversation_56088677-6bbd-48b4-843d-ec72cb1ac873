// 详情页样式
.detail-page {
  .content-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .detail-layout {
    display: grid;
    grid-template-columns: 1fr 1fr 320px;
    gap: 24px;
    align-items: start;
    margin-top: 24px;

    @media (max-width: 1200px) {
      grid-template-columns: 1fr 300px;
      
      .photos-section {
        grid-column: 1 / -1;
      }
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }

  // 关联信息样式
  .relation-info {
    .relation-group {
      &:not(:last-child) {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;
      }
    }

    .relation-type-tag {
      font-size: 13px;
      font-weight: 500;
      margin-bottom: 8px;
      border-radius: 4px;
    }

    .relation-item {
      margin-bottom: 8px;
      padding: 8px 12px;
      background: #fafafa;
      border-radius: 6px;
      border: 1px solid #f0f0f0;
      transition: all 0.2s ease;

      &:hover {
        background: #f5f5f5;
        border-color: #d9d9d9;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    .relation-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
    }

    .relation-item-name {
      font-weight: 500;
      color: #333;
      font-size: 13px;
    }

    .relation-item-type {
      font-size: 11px;
    }

    .relation-item-direction {
      color: #666;
      font-size: 12px;
      margin-bottom: 2px;
    }

    .relation-item-record {
      color: #666;
      font-size: 12px;
      line-height: 1.4;
    }
  }

  // 基本信息样式
  .basic-info {
    .info-item {
      display: flex;
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }

    .info-label {
      min-width: 80px;
      color: #666;
      font-size: 14px;
    }

    .info-value {
      flex: 1;
      color: #333;
      font-size: 14px;
      word-break: break-all;

      &.historical-records {
        line-height: 1.6;
        text-align: justify;
        margin-top: 4px;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 3px solid #1890ff;
      }
    }
  }

  // 图片展示样式
  .photos-section {
    .photo-item {
      margin-bottom: 12px;
      border-radius: 6px;
      overflow: hidden;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 返回按钮样式
  .back-button {
    margin-bottom: 24px;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    transition: all 0.2s ease;

    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }
  }

  // 标题样式
  .page-title {
    margin-bottom: 8px;
    text-align: center;
    color: #333;
  }

  .page-code {
    text-align: center;
    margin-bottom: 24px;

    .code-tag {
      padding: 4px 12px;
      background: #e6f4ff;
      color: #1677ff;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

// 卡片通用样式
.detail-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: none;
  height: 100%;

  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    font-size: 16px;
    font-weight: 600;
  }

  &.small-card .ant-card-head {
    font-size: 14px;
  }
}

// 空状态样式
.empty-state {
  margin: 20px 0;
  
  .ant-empty-description {
    color: #999;
  }
}
