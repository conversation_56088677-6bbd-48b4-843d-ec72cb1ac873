import PublicLayout from '@/components/PublicLayout';
import { getPublicElementRelationsByElement } from '@/services/elementRelation';
import {
  getPublicHistoricalElementDetail,
  getPublicHistoricalElementPhotos,
} from '@/services/historicalElement';
import {
  getPublicMountainDetail,
  getPublicMountainPhotos,
} from '@/services/mountain';
import {
  getPublicWaterSystemDetail,
  getPublicWaterSystemPhotos,
} from '@/services/waterSystem';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useParams, useSelector, useDispatch } from '@umijs/max';
import {
  Button,
  Card,
  Divider,
  Empty,
  Image,
  message,
  Tag,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import type { DictionaryState } from '@/models/dictionary';
import type { RegionDict, TypeDict } from '@/pages/Admin/Dictionary/dict-types';
import './styles.less';

const { Title } = Typography;

const DetailPage: React.FC = () => {
  const { type, id } = useParams();
  const numericId = Number(id);
  const dispatch = useDispatch();

  // 获取字典数据
  const { regionList, typeList } = useSelector((state: any) => state.dictionary);

  const [loading, setLoading] = useState<boolean>(false);
  const [detail, setDetail] = useState<any>(null);
  const [photos, setPhotos] = useState<
    Array<{ id: number; name: string; url: string }>
  >([]);
  const [relations, setRelations] = useState<API.ElementRelation[]>([]);
  const [relationsLoading, setRelationsLoading] = useState<boolean>(false);

  const getTypeName = () => {
    switch (type) {
      case 'mountain':
        return '山塬';
      case 'waterSystem':
        return '水系';
      case 'historicalElement':
        return '历史要素';
      default:
        return '未知类型';
    }
  };

  // 递归查找字典项
  const findDictItem = (list: any[], id: number): any => {
    for (const item of list) {
      if (item.id === id) {
        return item;
      }
      if (item.children && item.children.length > 0) {
        const found = findDictItem(item.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  // 获取区域名称
  const getRegionName = (regionDictId?: number) => {
    if (!regionDictId || !regionList || regionList.length === 0) {
      return detail?.regionDict?.regionName || '-';
    }
    const regionItem = findDictItem(regionList, regionDictId);
    return regionItem?.regionName || detail?.regionDict?.regionName || '-';
  };

  // 获取类型名称
  const getTypeDictName = (typeDictId?: number) => {
    if (!typeDictId || !typeList || typeList.length === 0) {
      return detail?.typeDict?.typeName || '-';
    }
    const typeItem = findDictItem(typeList, typeDictId);
    return typeItem?.typeName || detail?.typeDict?.typeName || '-';
  };

  // 获取关联关系数据
  const loadRelations = async () => {
    if (!type || !numericId) return;

    setRelationsLoading(true);
    try {
      // 根据接口文档，关联关系接口的路径参数
      const elementType =
        type === 'waterSystem'
          ? 'waterSystem'  // 接口文档中使用 waterSystem
          : type === 'historicalElement'
          ? 'historicalElement'  // 接口文档中使用 historicalElement
          : type;  // mountain 保持不变

      const response = await getPublicElementRelationsByElement(
        elementType,
        numericId,
      );
      if (response.errCode === 0) {
        setRelations(response.data || []);
      }
    } catch (error: any) {
      console.error('加载关联关系失败:', error);
      message.error('加载关联关系失败');
    } finally {
      setRelationsLoading(false);
    }
  };

  // 按关系类型分组关联数据
  const getGroupedRelations = () => {
    const grouped: { [key: string]: API.ElementRelation[] } = {};
    relations.forEach((relation) => {
      const relationName = relation.relationDict?.relationName || '其他关系';
      if (!grouped[relationName]) {
        grouped[relationName] = [];
      }
      grouped[relationName].push(relation);
    });
    return grouped;
  };

  // 获取目标要素的显示名称
  const getTargetElementName = (relation: API.ElementRelation) => {
    // 根据接口文档，优先使用 targetElement.name
    if (relation.targetElement) {
      return relation.targetElement.name;
    }
    // 如果没有 targetElement，使用 term 字段
    return relation.term || '未知要素';
  };

  // 获取目标要素类型的显示名称
  const getTargetElementTypeName = (relation: API.ElementRelation) => {
    // 根据接口文档，使用 targetEntityType 字段判断具体的要素类型
    switch (relation.targetEntityType) {
      case 'mountain':
        return '山塬';
      case 'water_system':
        return '水系';
      case 'historical_element':
        return '历史要素';
      case 'type_dict':
        return '类型';
      case 'region_dict':
        return '区域';
      default:
        return '其他';
    }
  };

  // 获取关系类型对应的颜色
  const getRelationColor = (relationName: string) => {
    const colorMap: { [key: string]: string } = {
      地理关系: 'blue',
      历史关系: 'green',
      文化关系: 'purple',
      空间关系: 'orange',
      时间关系: 'red',
      其他关系: 'default',
    };
    return colorMap[relationName] || 'default';
  };

  // 初始化字典数据
  useEffect(() => {
    if (!regionList || regionList.length === 0) {
      dispatch({ type: 'dictionary/fetchRegionList' });
    }
    if (!typeList || typeList.length === 0) {
      dispatch({ type: 'dictionary/fetchTypeList' });
    }
  }, [dispatch, regionList, typeList]);

  useEffect(() => {
    if (!type || !numericId) return;
    const load = async () => {
      setLoading(true);
      try {
        if (type === 'mountain') {
          const [d, p] = await Promise.all([
            getPublicMountainDetail(numericId),
            getPublicMountainPhotos(numericId),
          ]);
          if (d.errCode === 0) setDetail(d.data || null);
          if (p.errCode === 0) setPhotos(p.data || []);
        } else if (type === 'waterSystem') {
          const [d, p] = await Promise.all([
            getPublicWaterSystemDetail(numericId),
            getPublicWaterSystemPhotos(numericId),
          ]);
          if (d.errCode === 0) setDetail(d.data || null);
          if (p.errCode === 0) setPhotos(p.data || []);
        } else if (type === 'historicalElement') {
          const [d, p] = await Promise.all([
            getPublicHistoricalElementDetail(numericId),
            getPublicHistoricalElementPhotos(numericId),
          ]);
          if (d.errCode === 0) setDetail(d.data || null);
          if (p.errCode === 0) setPhotos(p.data || []);
        }
      } catch (e: any) {
        message.error(e?.message || '加载详情失败');
      } finally {
        setLoading(false);
      }
    };
    load();
    // 同时加载关联关系
    loadRelations();
  }, [type, numericId]);

  return (
    <PublicLayout>
      <div className="detail-page">
        <div
          className="content-card"
          style={{ padding: '24px', margin: '24px' }}
        >
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => window.history.back()}
            className="back-button"
          >
            返回
          </Button>

          <Title level={2} className="page-title">
            {detail?.name || getTypeName()}
          </Title>

          {detail?.code && (
            <div className="page-code">
              <span className="code-tag">{detail.code}</span>
            </div>
          )}

          <div className="detail-layout">
            <Card
              loading={loading}
              title="基本信息"
              className="detail-card"
            >
              <div className="basic-info">
                <div className="info-item">
                  <span className="info-label">名称：</span>
                  <span className="info-value">{detail?.name || '-'}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">编码：</span>
                  <span className="info-value">{detail?.code || '-'}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">所属区域：</span>
                  <span className="info-value">
                    {getRegionName(detail?.regionDictId)}
                  </span>
                </div>
                {(detail?.typeDictId || detail?.typeDict) && (
                  <div className="info-item">
                    <span className="info-label">所属类型：</span>
                    <span className="info-value">
                      {getTypeDictName(detail?.typeDictId)}
                    </span>
                  </div>
                )}

                {/* 山塬特有字段 */}
                {type === 'mountain' && (
                  <>
                    <div className="info-item">
                      <span className="info-label">海拔高度：</span>
                      <span className="info-value">
                        {detail?.height ? `${detail.height}米` : '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">地理坐标：</span>
                      <span className="info-value">
                        {detail?.longitude && detail?.latitude
                          ? `${detail.longitude}, ${detail.latitude}`
                          : '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">位置描述：</span>
                      <span className="info-value">{detail?.locationDescription || '-'}</span>
                    </div>
                  </>
                )}

                {/* 水系特有字段 */}
                {type === 'waterSystem' && (
                  <>
                    <div className="info-item">
                      <span className="info-label">长度面积：</span>
                      <span className="info-value">{detail?.lengthArea || '-'}</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">地理坐标：</span>
                      <span className="info-value">
                        {detail?.longitude && detail?.latitude
                          ? `${detail.longitude}, ${detail.latitude}`
                          : '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">位置描述：</span>
                      <span className="info-value">{detail?.locationDescription || '-'}</span>
                    </div>
                  </>
                )}

                {/* 历史要素特有字段 */}
                {type === 'historicalElement' && (
                  <>
                    <div className="info-item">
                      <span className="info-label">建造坐标：</span>
                      <span className="info-value">
                        {detail?.constructionLongitude && detail?.constructionLatitude
                          ? `${detail.constructionLongitude}, ${detail.constructionLatitude}`
                          : '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">位置描述：</span>
                      <span className="info-value">{detail?.locationDescription || '-'}</span>
                    </div>
                    {detail?.constructionTime && (
                      <div className="info-item">
                        <span className="info-label">建造时间：</span>
                        <span className="info-value">
                          {new Date(detail.constructionTime).toLocaleDateString('zh-CN')}
                        </span>
                      </div>
                    )}
                  </>
                )}

                {/* 历史记载 - 所有类型都有 */}
                {detail?.historicalRecords && (
                  <div className="info-item">
                    <span className="info-label">历史记载：</span>
                    <div className="info-value historical-records">
                      {detail.historicalRecords}
                    </div>
                  </div>
                )}
              </div>
            </Card>

            <Card
              loading={loading}
              title="相关图片"
              className="detail-card photos-section"
            >
              {photos?.length ? (
                <div
                  style={{ display: 'flex', flexDirection: 'column', gap: 12 }}
                >
                  {photos.map((p) => (
                    <Image
                      key={p.id}
                      src={p.url}
                      className="photo-item"
                      style={{
                        width: '100%',
                        maxHeight: 480,
                        objectFit: 'cover',
                        borderRadius: '6px',
                      }}
                    />
                  ))}
                </div>
              ) : (
                <div
                  style={{
                    color: '#999',
                    textAlign: 'center',
                    padding: '40px 0',
                    background: '#fafafa',
                    borderRadius: '6px',
                  }}
                >
                  暂无图片
                </div>
              )}
            </Card>

            <Card
              title="关联信息"
              loading={relationsLoading}
              className="detail-card relation-info"
              styles={{ body: { padding: '16px' } }}
            >
              {relations.length === 0 ? (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无关联信息"
                  className="empty-state"
                />
              ) : (
                <div>
                  {Object.entries(getGroupedRelations()).map(
                    ([relationName, relationList], index) => (
                      <div key={relationName} className="relation-group">
                        {index > 0 && <Divider style={{ margin: '16px 0' }} />}

                        <div style={{ marginBottom: 12 }}>
                          <Tag
                            color={getRelationColor(relationName)}
                            className="relation-type-tag"
                          >
                            {relationName} ({relationList.length})
                          </Tag>
                        </div>

                        <div style={{ paddingLeft: 8 }}>
                          {relationList.map((relation) => (
                            <div key={relation.id} className="relation-item">
                              <div className="relation-item-header">
                                <span className="relation-item-name">
                                  {getTargetElementName(relation)}
                                </span>
                                {relation.targetEntityType && (
                                  <Tag
                                    color="default"
                                    className="relation-item-type"
                                  >
                                    {getTargetElementTypeName(relation)}
                                  </Tag>
                                )}
                              </div>

                              {relation.direction && (
                                <div className="relation-item-direction">
                                  方向: {relation.direction}
                                </div>
                              )}

                              {relation.record && (
                                <div className="relation-item-record">
                                  {relation.record}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    ),
                  )}
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default DetailPage;
