/**
 * @file 字典数据dva model
 * @description 使用dva管理字典数据的全局缓存，方便其他页面引用
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */

import { RegionDict, RelationshipDict, TypeDict } from '@/pages/Admin/Dictionary/dict-types';
import {
  getRegionDictTree,
  getRelationshipDictTree,
  getTypeDictTree,
} from '@/services/dictionary';
import { AnyAction, Reducer } from '@umijs/max';
import { message } from 'antd';

// 递归过滤禁用状态的字典项
function filterEnabledDictItems<T extends { status: number; children?: T[] }>(
  items: T[],
): T[] {
  return items
    .filter((item) => item.status === 1) // 只保留启用状态的项
    .map((item) => ({
      ...item,
      children: item.children
        ? filterEnabledDictItems(item.children)
        : undefined,
    }));
}

// 字典状态接口
export interface DictionaryState {
  regionList: RegionDict[];
  typeList: TypeDict[];
  relationList: RelationshipDict[];
  loading: boolean;
}

// Model接口
interface IDictionaryModel {
  namespace: 'dictionary';
  state: DictionaryState;
  effects: {
    fetchRegionList: (
      action: { payload: Record<string, any> },
      effects: { call: Call; put: Put },
    ) => void;
    fetchTypeList: (
      action: { payload: Record<string, any> },
      effects: { call: Call; put: Put },
    ) => void;
    fetchRelationList: (
      action: { payload: Record<string, any> },
      effects: { call: Call; put: Put },
    ) => void;
    fetchAllLists: (
      action: { payload: Record<string, any> },
      effects: { call: Call; put: Put },
    ) => void;
  };
  reducers: {
    saveRegionList: Reducer<
      DictionaryState,
      { payload: RegionDict[] } & AnyAction
    >;
    saveTypeList: Reducer<DictionaryState, { payload: TypeDict[] } & AnyAction>;
    saveRelationList: Reducer<
      DictionaryState,
      { payload: RelationshipDict[] } & AnyAction
    >;
    updateLoading: Reducer<DictionaryState, { payload: boolean } & AnyAction>;
  };
}

const DictionaryModel: IDictionaryModel = {
  namespace: 'dictionary',

  state: {
    regionList: [],
    typeList: [],
    relationList: [],
    loading: false,
  } as DictionaryState,

  effects: {
    /** 查询列表 **/
    *fetchRegionList(
      _: { payload: Record<string, any> },
      { call, put }: { call: Call; put: Put },
    ) {
      yield put({ type: 'updateLoading', payload: true });
      try {
        const { errCode, msg, data } = yield call(getRegionDictTree);
        if (errCode) {
          message.error(msg || '区域列表查询失败');
        } else {
          yield put({ type: 'saveRegionList', payload: data });
        }
      } catch (error) {
        console.error('获取区域字典失败:', error);
      } finally {
        yield put({ type: 'updateLoading', payload: false });
      }
    },

    *fetchTypeList(
      _: { payload: Record<string, any> },
      { call, put }: { call: Call; put: Put },
    ) {
      yield put({ type: 'updateLoading', payload: true });
      try {
        const { errCode, msg, data } = yield call(getTypeDictTree);
        if (errCode) {
          message.error(msg || '获取类型字典失败');
        } else {
          yield put({ type: 'saveTypeList', payload: data });
        }
      } catch (error) {
        console.error('获取类型字典失败:', error);
      } finally {
        yield put({ type: 'updateLoading', payload: false });
      }
    },

    *fetchRelationList(
      _: { payload: Record<string, any> },
      { call, put }: { call: Call; put: Put },
    ) {
      yield put({ type: 'updateLoading', payload: true });
      try {
        const { errCode, msg, data } = yield call(getRelationshipDictTree);
        if (errCode) {
          message.error(msg || '获取关系字典失败');
        } else {
          yield put({ type: 'saveRelationList', payload: data });
        }
      } catch (error) {
        console.error('获取关系字典失败:', error);
      } finally {
        yield put({ type: 'updateLoading', payload: false });
      }
    },

    *fetchAllLists(
      _: { payload: Record<string, any> },
      { put }: { call: Call; put: Put },
    ) {
      yield put({ type: 'fetchRegionList' });
      yield put({ type: 'fetchTypeList' });
      yield put({ type: 'fetchRelationList' });
    },
  },

  reducers: {
    saveRegionList(state: DictionaryState, action: { payload: RegionDict[] }) {
      return {
        ...state,
        regionList: filterEnabledDictItems<RegionDict>(action.payload),
      };
    },

    saveTypeList(state: DictionaryState, action: { payload: TypeDict[] }) {
      return {
        ...state,
        typeList: filterEnabledDictItems<TypeDict>(action.payload),
      };
    },

    saveRelationList(
      state: DictionaryState,
      action: { payload: RelationshipDict[] },
    ) {
      return {
        ...state,
        relationList: filterEnabledDictItems(action.payload),
      };
    },

    updateLoading(state: DictionaryState, action: { payload: boolean }) {
      return {
        ...state,
        loading: action.payload,
      };
    },
  },
};

export default DictionaryModel;
