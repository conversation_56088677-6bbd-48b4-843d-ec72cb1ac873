import FullPageScroll, { scrollToSection } from '@/components/FullPageScroll';
import GaoDeMap from '@/components/GaoDeMap';
import PublicLayout from '@/components/PublicLayout';
import { getMarkerIcon } from '@/services/mapConfig';
import { getPortalMapMarkers, getPortalOverview } from '@/services/portal';
import {
  ArrowRightOutlined,
  DatabaseOutlined,
  EnvironmentOutlined,
  EyeOutlined,
  GlobalOutlined,
  HistoryOutlined,
} from '@ant-design/icons';
import { history } from '@umijs/max';
import { Button, Card, Col, Divider, Row, Space, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';

const { Title, Paragraph } = Typography;

const HomePage: React.FC = () => {
  const [overview, setOverview] = useState<{
    statistics?: {
      mountain: number;
      waterSystem: number;
      historicalElement: number;
    };
  } | null>(null);
  const [markers, setMarkers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // 添加全局处理函数
    (window as any).handleMarkerDetail = (
      markerId: string,
      type: string,
      encodedName: string,
    ) => {
      const name = decodeURIComponent(encodedName);
      console.log('🔍 查看详情点击:', { markerId, type, name });

      // 查找对应的标记点数据 (现在使用组合ID)
      const markerData = markers.find((m) => m.id === markerId);
      console.log('🔍 找到的标记点数据:', markerData);

      // 从组合ID中提取原始ID
      const originalId = markerId.split('_')[1];

      // 根据类型跳转到对应的详情页面
      let detailPath = '';
      switch (type) {
        case 'mountain':
          detailPath = `/detail/mountain/${originalId}`;
          break;
        case 'waterSystem':
          detailPath = `/detail/waterSystem/${originalId}`;
          break;
        case 'historicalElement':
          detailPath = `/detail/historicalElement/${originalId}`;
          break;
        default:
          console.error('未知的标记点类型:', type);
          return;
      }

      console.log('🔗 跳转到详情页面:', detailPath);

      // 跳转到详情页面
      history.push(detailPath);
    };

    const load = async () => {
      setLoading(true);
      setError(null);

      try {
        // 从后端获取数据
        const [ovRes, mkRes] = await Promise.all([
          getPortalOverview(),
          getPortalMapMarkers({
            types: ['mountain', 'waterSystem', 'historicalElement'],
          }),
        ]);

        // 设置概览数据
        if (ovRes.errCode === 0) {
          setOverview(ovRes.data || null);
        } else {
          console.error('概览数据获取失败:', ovRes);
        }

        // 设置地图标记点数据
        if (mkRes.errCode === 0 && mkRes.data) {
          console.log('✅ 获取到标记点数据:', mkRes.data.length, '个');

          // 过滤有效的标记点数据
          const validMarkers = mkRes.data.filter((m: any) => {
            const hasLng =
              m.longitude !== null &&
              m.longitude !== undefined &&
              !isNaN(Number(m.longitude));
            const hasLat =
              m.latitude !== null &&
              m.latitude !== undefined &&
              !isNaN(Number(m.latitude));
            return hasLng && hasLat;
          });

          // 转换为地图标记点格式
          const ms = validMarkers.map((m: any) => {
            const lng = Number(m.longitude);
            const lat = Number(m.latitude);

            const iconConfig = getMarkerIcon(m.type);

            // 预处理名称以避免模板字符串中的特殊字符问题
            const encodedName = encodeURIComponent(m.name);

            // 使用默认图标配置如果 iconConfig 为空
            const finalIconConfig = iconConfig || {
              image: '/images/markers/default.png',
              size: [32, 32] as [number, number],
              offset: [-16, -16] as [number, number],
            };

            const markerData = {
              id: `${m.type}_${m.id}`, // 使用类型+ID作为唯一标识
              title: m.name,
              position: [lng, lat] as [number, number],
              icon: {
                image: finalIconConfig.image,
                size: finalIconConfig.size,
                offset: finalIconConfig.offset,
              },
              extData: { type: m.type, ...m },
              content: `
                <div style="
                  padding: 0;
                  min-width: 280px;
                  max-width: 320px;
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                  line-height: 1.4;
                  background: white;
                  border-radius: 8px;
                  overflow: hidden;
                  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                ">
                  <div style="
                    width: 100%;
                    height: 120px;
                    background: ${
                      m.thumbnailUrl
                        ? `url('${m.thumbnailUrl}')`
                        : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                    };
                    background-size: cover;
                    background-position: center;
                    position: relative;
                  ">
                    ${
                      !m.thumbnailUrl
                        ? `
                      <div style="
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        color: white;
                        font-size: 14px;
                        text-align: center;
                      ">暂无图片</div>
                    `
                        : ''
                    }
                    <div style="
                      position: absolute;
                      bottom: 0;
                      left: 0;
                      right: 0;
                      background: linear-gradient(transparent, rgba(0,0,0,0.6));
                      height: 40px;
                    "></div>
                  </div>
                  <div style="padding: 16px;">
                    <h3 style="
                      margin: 0 0 8px 0;
                      font-size: 18px;
                      font-weight: 600;
                      color: #333;
                    ">${m.name}</h3>
                    <p style="
                      margin: 0 0 6px 0;
                      font-size: 14px;
                      color: #666;
                    ">类型: ${
                      m.type === 'mountain'
                        ? '山塬'
                        : m.type === 'waterSystem'
                        ? '水系'
                        : '历史要素'
                    }</p>
                    <p style="
                      margin: 0 0 12px 0;
                      font-size: 14px;
                      color: #666;
                      line-height: 1.5;
                    ">${m.summary || '暂无描述'}</p>
                    <button style="
                      background: #1890ff;
                      color: white;
                      border: none;
                      padding: 8px 16px;
                      border-radius: 4px;
                      font-size: 14px;
                      cursor: pointer;
                      width: 100%;
                      font-weight: 500;
                    " onclick="window.handleMarkerDetail('${m.type}_${
                m.id
              }', '${m.type}', '${encodedName}')">查看详情</button>
                  </div>
                </div>
              `,
            };

            // 只在类型不匹配时输出详细调试信息
            if (!iconConfig) {
              console.log(`� ${m.name} 详细信息:`, {
                id: m.id,
                type: m.type,
                extDataType: markerData.extData.type,
              });
            }

            return markerData;
          });

          console.log('🎯 成功处理标记点:', ms.length, '个');

          setMarkers(ms);
        } else {
          console.error('地图标记点数据获取失败:', mkRes);
          setMarkers([]);
          setError('地图数据加载失败');
        }
      } catch (e: any) {
        console.error('❌ 接口请求失败:', e?.message);
        console.error('错误详情:', e);
        setError(`接口请求失败: ${e?.message || '未知错误'}`);
        // 清空数据，不使用mock
        setOverview(null);
        setMarkers([]);
      } finally {
        setLoading(false);
      }
    };
    load();

    // 清理函数
    return () => {
      delete (window as any).handleMarkerDetail;
    };
  }, []);

  // 生成信息窗口内容，包含图片和查看详情按钮
  // const generateInfoWindowContent = (item: any, type: string) => {
  //   const detailUrl = `/detail/${type}/${item.id}`;
  //   let basicInfo = '';

  //   if (type === 'mountain') {
  //     basicInfo = `<p style="margin: 4px 0; color: #666; font-size: 13px;">海拔：${item.height}米</p>`;
  //   } else if (type === 'waterSystem') {
  //     basicInfo = `<p style="margin: 4px 0; color: #666; font-size: 13px;">长度：${item.length_area}</p>`;
  //   } else if (type === 'historicalElement') {
  //     basicInfo = `<p style="margin: 4px 0; color: #666; font-size: 13px;">建造时间：${new Date(
  //       item.construction_time,
  //     ).getFullYear()}年</p>`;
  //   }

  //   // 获取第一张图片作为展示图片
  //   const firstPhoto =
  //     item.photos && item.photos.length > 0 ? item.photos[0] : null;
  //   const imageHtml = firstPhoto
  //     ? `<div style="position: relative; width: 100%; height: 140px; overflow: hidden;">
  //         <img src="${firstPhoto.url}" alt="${firstPhoto.name || item.name}"
  //              style="width: 100%; height: 100%; object-fit: cover; display: block;"
  //              onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
  //         <div style="display: none; width: 100%; height: 100%; background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%); align-items: center; justify-content: center; color: #999; font-size: 14px;">暂无图片</div>
  //        </div>`
  //     : ''; // `<div style="width: 100%; height: 140px; background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%); display: flex; align-items: center; justify-content: center; color: #999; font-size: 14px;">暂无图片</div>`;

  //   return `<div style="padding: 0; min-width: 240px; max-width: 300px; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); overflow: hidden;">
  //     ${imageHtml}
  //     <div style="padding: 12px;">
  //       <h4 style="margin: 0 0 6px 0; color: #333; font-size: 16px; font-weight: bold; line-height: 1.3;">${item.name}</h4>
  //       ${basicInfo}
  //       <p style="margin: 6px 0 12px 0; color: #666; font-size: 13px; line-height: 1.4; max-height: 54px; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical;">${item.historical_records}</p>
  //       <div style="text-align: center;">
  //         <a href="${detailUrl}" style="display: inline-block; padding: 8px 20px; background: #1890ff; color: white; text-decoration: none; border-radius: 4px; font-size: 13px; font-weight: 500; transition: all 0.3s ease;"
  //            onmouseover="this.style.backgroundColor='#40a9ff'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(24,144,255,0.3)';"
  //            onmouseout="this.style.backgroundColor='#1890ff'; this.style.transform='translateY(0)'; this.style.boxShadow='none';">查看详情</a>
  //       </div>
  //     </div>
  //   </div>`;
  // };

  // 准备地图标记点数据
  const prepareMapMarkers = () => markers;

  // 自定义标记点渲染函数
  const renderMarker = (marker: any) => {
    // 从marker的extData中获取类型信息
    const markerType = marker.extData?.type || 'mountain';
    const iconConfig = getMarkerIcon(markerType);
    const typeNames = {
      mountain: '山塬',
      waterSystem: '水系',
      historicalElement: '历史要素',
    };
    const typeName = typeNames[markerType as keyof typeof typeNames] || '未知';

    return `
      <div style="
        position: relative;
        width: 32px;
        height: 32px;
        cursor: pointer;
        transition: all 0.3s ease;
      "
      onmouseover="this.style.transform='scale(1.2)'; this.style.zIndex='1000';"
      onmouseout="this.style.transform='scale(1)'; this.style.zIndex='auto';"
      title="${marker.title} (${typeName})">
        <img src="${iconConfig.image}"
             style="
               width: 32px;
               height: 32px;
               object-fit: contain;
               filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
             "
             alt="${typeName}" />
      </div>
    `;
  };

  // 地图标记点点击事件处理
  const handleMarkerClick = (marker: any, e: any) => {
    console.log('标记点点击:', marker, e);
    // 不再显示自定义Modal，统一使用地图信息窗口
  };

  // 地图点击事件处理
  const handleMapClick = (e: any) => {
    console.log('地图点击:', e);
  };

  // 地图创建完成回调
  const handleMapCreated = (mapInstance: any) => {
    console.log('地图创建完成:', mapInstance);
  };

  // 导航到详情页面
  const navigateToSection = (path: string) => {
    history.push(path);
  };

  // 统计数据
  const statisticsData = [
    {
      title: '山塬数量',
      value: overview?.statistics?.mountain || 0,
      icon: <EnvironmentOutlined style={{ color: '#52c41a' }} />,
      suffix: '座',
    },
    {
      title: '水系数量',
      value: overview?.statistics?.waterSystem || 0,
      icon: <GlobalOutlined style={{ color: '#1890ff' }} />,
      suffix: '条',
    },
    {
      title: '历史要素',
      value: overview?.statistics?.historicalElement || 0,
      icon: <HistoryOutlined style={{ color: '#fa8c16' }} />,
      suffix: '处',
    },
    {
      title: '数据总量',
      value:
        (overview?.statistics?.mountain || 0) +
        (overview?.statistics?.waterSystem || 0) +
        (overview?.statistics?.historicalElement || 0),
      icon: <DatabaseOutlined style={{ color: '#722ed1' }} />,
      suffix: '项',
    },
  ];

  // 滚动到地图区域的函数
  const scrollToMap = () => {
    // 使用导出的滚动函数
    scrollToSection(1);
  };

  return (
    <PublicLayout>
      <FullPageScroll>
        {/* 第一屏：Hero Banner */}
        <div className="hero-banner">
          {/* 装饰性几何元素 */}
          <div className="hero-decorations">
            <div className="decoration-shape decoration-shape-1"></div>
            <div className="decoration-shape decoration-shape-2"></div>
            <div className="decoration-shape decoration-shape-3"></div>
            <div className="decoration-line decoration-line-1"></div>
            <div className="decoration-line decoration-line-2"></div>
          </div>

          <div className="hero-content">
            <div className="hero-text">
              <Title level={1} className="hero-title">
                关中地区智慧营建系统
              </Title>
              <Paragraph className="hero-description">
                探索关中地区的山塬、水系与历史要素，感受千年文明的智慧传承
              </Paragraph>

              {/* 统计数据 */}
              <div className="hero-statistics">
                <div className="stats-container">
                  {statisticsData.map((stat, index) => (
                    <div key={index} className="stat-item">
                      <div className="stat-number">
                        <span className="stat-value" data-value={stat.value}>
                          {stat.value}
                        </span>
                        <span className="stat-suffix">{stat.suffix}</span>
                      </div>
                      <div className="stat-label">{stat.title}</div>
                    </div>
                  ))}
                </div>
              </div>

              <Space size="large" className="hero-actions">
                <Button
                  type="primary"
                  size="large"
                  icon={<EyeOutlined />}
                  onClick={() => navigateToSection('/digital')}
                >
                  数据可视化
                </Button>
                <Button
                  size="large"
                  icon={<ArrowRightOutlined />}
                  onClick={() => navigateToSection('/mountain')}
                >
                  开始探索
                </Button>
              </Space>
            </div>
          </div>

          {/* 滚动指示器 */}
          <div className="scroll-indicator" onClick={scrollToMap}>
            <div className="scroll-text">探索地理分布</div>
            <div className="scroll-arrow">
              <ArrowRightOutlined style={{ transform: 'rotate(90deg)' }} />
            </div>
          </div>

          <div className="hero-overlay"></div>
        </div>

        {/* 第二屏：地图展示 */}
        <div className="map-section">
          <div className="container">
            <div className="section-header">
              <Title level={2} className="section-title">
                地理分布
              </Title>
              <Paragraph className="section-description">
                点击地图标记点查看详细信息
              </Paragraph>
            </div>

            <Card className="map-card">
              {/* 状态显示 */}
              {loading && (
                <div
                  style={{
                    position: 'absolute',
                    top: '20px',
                    left: '20px',
                    background: 'rgba(255,255,255,0.9)',
                    padding: '8px 16px',
                    borderRadius: '4px',
                    zIndex: 1000,
                    fontSize: '14px',
                    color: '#1890ff',
                  }}
                >
                  🔄 正在加载地图数据...
                </div>
              )}
              {error && (
                <div
                  style={{
                    position: 'absolute',
                    top: '20px',
                    left: '20px',
                    background: 'rgba(255,0,0,0.1)',
                    padding: '8px 16px',
                    borderRadius: '4px',
                    zIndex: 1000,
                    fontSize: '14px',
                    color: '#ff4d4f',
                    border: '1px solid #ff4d4f',
                  }}
                >
                  ❌ {error}
                </div>
              )}
              {!loading && !error && markers.length === 0 && (
                <div
                  style={{
                    position: 'absolute',
                    top: '20px',
                    left: '20px',
                    background: 'rgba(255,255,255,0.9)',
                    padding: '8px 16px',
                    borderRadius: '4px',
                    zIndex: 1000,
                    fontSize: '14px',
                    color: '#faad14',
                  }}
                >
                  ⚠️ 暂无地图数据
                </div>
              )}
              {!loading && !error && markers.length > 0 && (
                <div
                  style={{
                    position: 'absolute',
                    top: '20px',
                    left: '20px',
                    background: 'rgba(0,255,0,0.1)',
                    padding: '8px 16px',
                    borderRadius: '4px',
                    zIndex: 1000,
                    fontSize: '14px',
                    color: '#52c41a',
                    border: '1px solid #52c41a',
                  }}
                >
                  ✅ 已加载 {markers.length} 个标记点
                </div>
              )}

              <div id="map-container" className="map-container">
                <GaoDeMap
                  city="西安"
                  center={[109.2, 34.8]}
                  zoom={7}
                  activedZoom={10}
                  markers={prepareMapMarkers()}
                  enableInfoWindow={true}
                  enableCluster={false}
                  markerRender={renderMarker}
                  events={{
                    onClick: handleMapClick,
                    onMarkerClick: handleMarkerClick,
                  }}
                  onMapCreated={handleMapCreated}
                  style={{ width: '100%', height: '100%' }}
                />

                {/* 地图图例 */}
                <div className="map-legend">
                  <div className="legend-title">图例</div>
                  <div className="legend-items">
                    <div className="legend-item">
                      <img
                        src="/images/markers/mountain.png"
                        alt="山塬"
                        style={{ width: '16px', height: '16px' }}
                      />
                      <span>山塬</span>
                    </div>
                    <div className="legend-item">
                      <img
                        src="/images/markers/water.png"
                        alt="水系"
                        style={{ width: '16px', height: '16px' }}
                      />
                      <span>水系</span>
                    </div>
                    <div className="legend-item">
                      <img
                        src="/images/markers/historical.png"
                        alt="历史要素"
                        style={{ width: '16px', height: '16px' }}
                      />
                      <span>历史要素</span>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* 第三屏：页脚信息 */}
        <div className="footer-section">
          <div className="container">
            <Row gutter={[48, 32]}>
              <Col xs={24} md={8}>
                <div className="footer-brand">
                  <Title level={4} style={{ color: 'white', marginBottom: 16 }}>
                    智慧营建系统
                  </Title>
                  <Paragraph style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                    探索关中地区的山塬、水系与历史要素，感受千年文明的智慧传承
                  </Paragraph>
                </div>
              </Col>
              <Col xs={24} md={8}>
                <div className="footer-links">
                  <Title level={5} style={{ color: 'white', marginBottom: 16 }}>
                    快速导航
                  </Title>
                  <div className="footer-nav">
                    <a onClick={() => navigateToSection('/mountain')}>
                      山塬地貌
                    </a>
                    <a onClick={() => navigateToSection('/water-system')}>
                      水系网络
                    </a>
                    <a onClick={() => navigateToSection('/historical-element')}>
                      历史要素
                    </a>
                    <a onClick={() => navigateToSection('/digital')}>数字化</a>
                  </div>
                </div>
              </Col>
              <Col xs={24} md={8}>
                <div className="footer-contact">
                  <Title level={5} style={{ color: 'white', marginBottom: 16 }}>
                    系统信息
                  </Title>
                  <div className="footer-info">
                    <p>基于现代Web技术构建</p>
                    <p>支持多终端访问</p>
                    <p>实时数据更新</p>
                  </div>
                </div>
              </Col>
            </Row>
            <Divider
              style={{
                borderColor: 'rgba(255, 255, 255, 0.2)',
                margin: '40px 0 20px',
              }}
            />
            <div className="footer-bottom">
              <Paragraph
                style={{
                  color: 'rgba(255, 255, 255, 0.6)',
                  textAlign: 'center',
                  margin: 0,
                }}
              >
                © 2024 关中地区智慧营建系统. All rights reserved.
              </Paragraph>
            </div>
          </div>
        </div>
      </FullPageScroll>
    </PublicLayout>
  );
};

export default HomePage;
